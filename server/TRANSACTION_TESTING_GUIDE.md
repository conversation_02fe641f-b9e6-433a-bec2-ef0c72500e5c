# Transaction Support in In-Memory Database Testing

## Overview

Yes, you **CAN** use transactions with in-memory databases, but the setup depends on which in-memory database you're using.

## MongoDB Memory Server (Recommended)

### ✅ **Transaction Support: YES** (with proper configuration)

MongoDB Memory Server supports transactions when configured as a **replica set**. Single-node MongoDB instances don't support transactions, but replica sets do.

### Configuration

Your test setup now supports both modes:

1. **In-Memory with Transactions** (default): Uses `MongoMemoryReplSet`
2. **External Test Database**: Uses your local/remote test database

### Environment Variables

```bash
# .env.test
USE_MEMORY_DB=true    # Use MongoDB Memory Server with transaction support
USE_MEMORY_DB=false   # Use external test database
```

### Benefits of In-Memory Database with Transactions

1. **✅ Complete Isolation**: Each test run gets a fresh database
2. **✅ Transaction Support**: Full ACID compliance
3. **✅ Speed**: Faster than external databases
4. **✅ No Setup Required**: No need to install/configure MongoDB
5. **✅ Parallel Testing**: Multiple test suites can run simultaneously
6. **✅ CI/CD Friendly**: Works in any environment

### Usage Examples

#### Basic Transaction Test
```typescript
import { TransactionManager } from '../api/services/transactionManager.service';

test('should use transactions', async () => {
  const result = await TransactionManager.executeInTransaction(async (session) => {
    const user = new User({ name: 'Test', email: '<EMAIL>' });
    await user.save({ session });
    
    const restaurant = new RestaurantModel({ 
      name: 'Test Restaurant',
      currentOwner: user._id 
    });
    await restaurant.save({ session });
    
    return { user, restaurant };
  });
  
  // Both documents created atomically
  expect(result.user).toBeDefined();
  expect(result.restaurant).toBeDefined();
});
```

#### Rollback on Error
```typescript
test('should rollback on error', async () => {
  try {
    await TransactionManager.executeInTransaction(async (session) => {
      const user = new User({ name: 'Test', email: '<EMAIL>' });
      await user.save({ session });
      
      // This will fail and rollback the entire transaction
      throw new Error('Simulated error');
    });
  } catch (error) {
    // Expected error
  }
  
  // No user should be created due to rollback
  const userCount = await User.countDocuments();
  expect(userCount).toBe(0);
});
```

## Alternative: SQLite In-Memory

If you prefer SQL databases, SQLite in-memory also supports transactions:

```typescript
// Example with SQLite (if you switch to SQL)
import Database from 'better-sqlite3';

const db = new Database(':memory:'); // In-memory SQLite

// Transactions work perfectly
const transaction = db.transaction((users) => {
  for (const user of users) {
    db.prepare('INSERT INTO users (name, email) VALUES (?, ?)').run(user.name, user.email);
  }
});

transaction([
  { name: 'User 1', email: '<EMAIL>' },
  { name: 'User 2', email: '<EMAIL>' }
]);
```

## Running Tests

### With In-Memory Database (Transactions Supported)
```bash
# Set environment variable
export USE_MEMORY_DB=true

# Run tests
npm test
```

### With External Test Database
```bash
# Set environment variable
export USE_MEMORY_DB=false
export TEST_DATABASE_URL=mongodb://localhost:27017/restaurant-test

# Run tests
npm test
```

### Test Transaction Support
```bash
# Run the specific transaction test
npm test -- tests/transaction-support.test.ts
```

## Best Practices

1. **Use Transactions for Multi-Document Operations**
   ```typescript
   // ✅ Good: Atomic operation
   await TransactionManager.executeInTransaction(async (session) => {
     await user.save({ session });
     await restaurant.save({ session });
     await ownership.save({ session });
   });
   ```

2. **Handle Transaction Failures Gracefully**
   ```typescript
   try {
     await TransactionManager.executeInTransaction(operation);
   } catch (error) {
     // Handle rollback scenario
     logger.error('Transaction failed:', error);
   }
   ```

3. **Use Optional Transactions for Compatibility**
   ```typescript
   // Works with or without transaction support
   await TransactionManager.executeWithOptionalTransaction(async (session) => {
     await document.save(session ? { session } : {});
   });
   ```

## Performance Considerations

- **In-Memory**: ~50-100ms per test
- **External DB**: ~200-500ms per test
- **Memory Usage**: ~50-100MB for in-memory server

## Troubleshooting

### "Transaction numbers are only allowed on a replica set member or mongos"
- **Solution**: Use `MongoMemoryReplSet` instead of `MongoMemoryServer`
- **Status**: ✅ Fixed in current setup

### Tests clearing production data
- **Solution**: Safety checks prevent production database usage
- **Status**: ✅ Fixed with database URL validation

### Slow test execution
- **Solution**: Use in-memory database for faster tests
- **Status**: ✅ Available with `USE_MEMORY_DB=true`

## Conclusion

**Yes, you can absolutely use transactions with in-memory databases!** The current setup provides:

- ✅ Full transaction support with MongoDB Memory Server
- ✅ Safety checks to prevent production data loss
- ✅ Flexible configuration for different environments
- ✅ Fast, isolated test execution
