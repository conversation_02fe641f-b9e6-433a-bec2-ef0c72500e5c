import mongoose from 'mongoose';
import { TransactionManager } from '../api/services/transactionManager.service';
import { User } from '../api/models/user.model';
import RestaurantModel from '../api/models/restaurant.model';

describe('Transaction Support Tests', () => {
  beforeAll(async () => {
    // Connect to test database (will use memory server if configured)
    const testDbUrl = process.env.MONGO_TEST || 'mongodb://localhost:27017/restaurant_test';
    await mongoose.connect(testDbUrl);
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clean up
    await User.deleteMany({});
    await RestaurantModel.deleteMany({});
  });

  test('should support transactions in memory database', async () => {
    const supportsTransactions = await TransactionManager.supportsTransactions();
    console.log('Transaction support:', supportsTransactions);
    
    // With MongoMemoryReplSet, this should be true
    expect(supportsTransactions).toBe(true);
  });

  test('should execute operations in transaction successfully', async () => {
    const result = await TransactionManager.executeInTransaction(async (session) => {
      // Create user within transaction
      const user = new User({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        userRole: 'admin',
        gender: 'Male'
      });
      await user.save({ session });

      // Create restaurant within transaction
      const restaurant = new RestaurantModel({
        name: 'Test Restaurant',
        address: 'Test Address',
        phone: '1234567890',
        logo: 'test-logo.jpg',
        subStart: new Date(),
        subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        currentOwner: user._id
      });
      await restaurant.save({ session });

      return { user, restaurant };
    });

    // Verify both documents were created
    expect(result.user).toBeDefined();
    expect(result.restaurant).toBeDefined();

    const savedUser = await User.findById(result.user._id);
    const savedRestaurant = await RestaurantModel.findById(result.restaurant._id);

    expect(savedUser).toBeTruthy();
    expect(savedRestaurant).toBeTruthy();
    expect(savedRestaurant?.currentOwner?.toString()).toBe(result.user._id?.toString());
  });

  test('should rollback transaction on error', async () => {
    try {
      await TransactionManager.executeInTransaction(async (session) => {
        // Create user within transaction
        const user = new User({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'password123',
          userRole: 'admin',
          gender: 'Male'
        });
        await user.save({ session });

        // This should cause an error (missing required fields)
        const restaurant = new RestaurantModel({
          name: 'Test Restaurant'
          // Missing required fields like address, phone, email
        });
        await restaurant.save({ session });

        return { user, restaurant };
      });
    } catch (error) {
      // Transaction should have failed
      expect(error).toBeDefined();
    }

    // Verify no documents were created due to rollback
    const userCount = await User.countDocuments();
    const restaurantCount = await RestaurantModel.countDocuments();
    
    expect(userCount).toBe(0);
    expect(restaurantCount).toBe(0);
  });

  test('should handle concurrent operations with transactions', async () => {
    // Create initial user
    const user = new User({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      userRole: 'admin',
      gender: 'Male'
    });
    await user.save();

    // Simulate concurrent operations
    const operations = Array.from({ length: 5 }, (_, i) => 
      TransactionManager.executeInTransaction(async (session) => {
        const restaurant = new RestaurantModel({
          name: `Restaurant ${i}`,
          address: `Address ${i}`,
          phone: `123456789${i}`,
          logo: `test-logo-${i}.jpg`,
          subStart: new Date(),
          subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          currentOwner: user._id
        });
        await restaurant.save({ session });
        return restaurant;
      })
    );

    const results = await Promise.all(operations);
    
    // All operations should succeed
    expect(results).toHaveLength(5);
    
    // Verify all restaurants were created
    const restaurantCount = await RestaurantModel.countDocuments();
    expect(restaurantCount).toBe(5);
  });

  test('should work with executeWithOptionalTransaction', async () => {
    const result = await TransactionManager.executeWithOptionalTransaction(async (session) => {
      const user = new User({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        userRole: 'admin',
        gender: 'Male'
      });
      
      if (session) {
        await user.save({ session });
      } else {
        await user.save();
      }
      
      return user;
    });

    expect(result).toBeDefined();
    
    const savedUser = await User.findById(result._id);
    expect(savedUser).toBeTruthy();
  });
});
