import mongoose from 'mongoose';
import { MongoMemoryReplSet } from 'mongodb-memory-server';

let mongoServer: MongoMemoryReplSet;

// Global test setup
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.REFRESH_TOKEN_SECRET = 'test-refresh-secret';

  // Check if we should use in-memory database
  const useInMemory = process.env.USE_MEMORY_DB === 'true' || !process.env.TEST_DATABASE_URL;

  if (useInMemory) {
    // Start MongoDB Memory Server with replica set for transaction support
    mongoServer = await MongoMemoryReplSet.create({
      replSet: {
        count: 1, // Single node replica set
        storageEngine: 'wiredTiger',
      },
    });

    const uri = mongoServer.getUri();
    process.env.MONGO_TEST = uri;
    console.log('Using MongoDB Memory Server with transaction support:', uri);
  } else {
    // Use external test database
    const testDbUrl = process.env.TEST_DATABASE_URL || 'mongodb://localhost:27017/restaurant-test';

    // SAFETY CHECK: Prevent using production database URLs in tests
    if (testDbUrl.includes('cluster0.awfwbid.mongodb.net') ||
        testDbUrl.includes('cluster0.prwg7.mongodb.net') ||
        !testDbUrl.includes('test')) {
      throw new Error('DANGER: Tests are configured to use production database! Please use a test database.');
    }

    process.env.MONGO_TEST = testDbUrl;
    console.log('Using external test database:', testDbUrl);
  }

  // Increase timeout for database operations
  jest.setTimeout(30000);
});

// Global test cleanup
afterAll(async () => {
  // Close mongoose connections
  await mongoose.disconnect();

  // Stop in-memory MongoDB instance
  if (mongoServer) {
    await mongoServer.stop();
    console.log('MongoDB Memory Server stopped');
  }
});

// Setup for each test file
beforeEach(async () => {
  // Clear all collections before each test
  if (mongoose.connection.readyState === 1) {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
  }
});

// Mock console methods to reduce noise in test output
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock external services
jest.mock('../config/cloudinary', () => ({
  uploader: {
    upload: jest.fn().mockResolvedValue({
      url: 'https://test-cloudinary-url.com/test-image.jpg',
      public_id: 'test-public-id',
    }),
    destroy: jest.fn().mockResolvedValue({ result: 'ok' }),
  },
}));

// Mock file upload utilities
jest.mock('../api/utilities/multer', () => ({
  upload: {
    single: jest.fn(() => (req: any, res: any, next: any) => next()),
    array: jest.fn(() => (req: any, res: any, next: any) => next()),
    fields: jest.fn(() => (req: any, res: any, next: any) => next()),
    any: jest.fn(() => (req: any, res: any, next: any) => next()),
  },
  processImage: jest.fn().mockResolvedValue({
    url: 'https://test-image-url.com/test.jpg',
    public_id: 'test-id',
  }),
  uploadToCloudinary: jest.fn().mockResolvedValue({
    url: 'https://test-cloudinary-url.com/test.jpg',
    public_id: 'test-id',
  }),
}));

// Helper function to create test database connection
export const createTestConnection = async () => {
  if (mongoose.connection.readyState === 0) {
    const testDbUrl = process.env.MONGO_TEST || 'mongodb://localhost:27017/restaurant-test';

    // Additional safety check
    if (testDbUrl.includes('cluster0.awfwbid.mongodb.net') ||
        testDbUrl.includes('cluster0.prwg7.mongodb.net') ||
        !testDbUrl.includes('test')) {
      throw new Error('DANGER: Attempting to connect to production database in tests!');
    }

    await mongoose.connect(testDbUrl);
  }
  return mongoose.connection;
};

// Helper function to close test database connection
export const closeTestConnection = async () => {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }
};

// Helper function to clear all test data
export const clearTestData = async () => {
  if (mongoose.connection.readyState === 1) {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
  }
};

// Setup and teardown functions for individual test files
export const setupTestEnvironment = async () => {
  await createTestConnection();
};

export const teardownTestEnvironment = async () => {
  await closeTestConnection();
};

export const clearDatabase = async () => {
  await clearTestData();
};

// Generate test utilities
const generateObjectId = () => {
  return new mongoose.Types.ObjectId();
};

const generateTestToken = (userId: string, userRole: string = 'admin') => {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    {
      userId,
      userRole,
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
    },
    process.env.JWT_SECRET || 'test-jwt-secret'
  );
};

// Export test utilities
export const testUtils = {
  createTestConnection,
  closeTestConnection,
  clearTestData,
  generateObjectId,
  generateTestToken,
};
