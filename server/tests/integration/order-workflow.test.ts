import request from "supertest";
import { app } from "../../app/app";
import {
  setupTestEnvironment,
  teardownTestEnvironment,
  clearDatabase,
  testUtils,
} from "../setup";
import Restaurant from "../../api/models/restaurant.model";
import { User } from "../../api/models/user.model";
import { MenuItemModel } from "../../api/models/menu.model";
import { Table } from "../../api/models/table.model";
import Item from "../../api/models/item.model";
import { Order } from "../../api/models/order.model";
import {
  OrderStateMachine,
  OrderState,
} from "../../api/services/orderStateMachine.service";
import mongoose from "mongoose";

describe("Order Workflow Integration Tests", () => {
  let restaurant: any;
  let adminUser: any;
  let waiterUser: any;
  let kitchenUser: any;
  let adminToken: string;
  let waiterToken: string;
  let kitchenToken: string;
  let menuItem: any;
  let table: any;
  let ingredient: any;

  beforeAll(async () => {
    await setupTestEnvironment();
  });

  afterAll(async () => {
    await teardownTestEnvironment();
  });

  beforeEach(async () => {
    await clearDatabase();

    // Create test restaurant
    restaurant = await Restaurant.create({
      name: "Test Restaurant",
      email: "<EMAIL>",
      phone: "1234567890",
      address: "123 Test St",
    });

    // Create test users with different roles
    adminUser = await User.create({
      name: "Admin User",
      email: "<EMAIL>",
      password: "AdminPassword123!",
      userRole: "admin",
      restaurant: restaurant._id,
      isActive: true,
    });

    waiterUser = await User.create({
      name: "Waiter User",
      email: "<EMAIL>",
      password: "WaiterPassword123!",
      userRole: "waiter",
      restaurant: restaurant._id,
      isActive: true,
    });

    kitchenUser = await User.create({
      name: "Kitchen User",
      email: "<EMAIL>",
      password: "KitchenPassword123!",
      userRole: "kitchen_staff",
      restaurant: restaurant._id,
      isActive: true,
    });

    // Generate tokens
    adminToken = testUtils.generateTestToken(adminUser._id.toString(), "admin");
    waiterToken = testUtils.generateTestToken(
      waiterUser._id.toString(),
      "waiter"
    );
    kitchenToken = testUtils.generateTestToken(
      kitchenUser._id.toString(),
      "kitchen_staff"
    );

    // Create test ingredient
    ingredient = await Item.create({
      name: "Test Ingredient",
      type: "ingredient",
      category: "vegetables",
      threshold: 10,
      initialStock: 100,
      remainingStock: 100,
      usedStock: 0,
      ingredientType: "raw_material",
      costPerUnit: 5.0,
      unit: "kg",
      restaurant: restaurant._id,
      isActive: true,
    });

    // Create test menu item
    menuItem = await MenuItemModel.create({
      name: "Test Burger",
      description: "Delicious test burger",
      price: 15.99,
      category: "main",
      restaurant: restaurant._id,
      isActive: true,
      ingredients: [
        {
          ingredient: ingredient._id,
          quantity: 0.2, // 200g per burger
        },
      ],
    });

    // Create test table
    table = await Table.create({
      name: "Table 1",
      capacity: 4,
      status: "available",
      restaurant: restaurant._id,
      isActive: true,
    });
  });

  describe("Complete Order Lifecycle", () => {
    test("should handle complete order workflow from creation to payment", async () => {
      // Step 1: Create order (waiter)
      const createOrderResponse = await request(app)
        .post("/api/order")
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({
          orderType: "dine_in",
          table: [table._id],
          menuItems: [
            {
              item: menuItem._id,
              quantity: 2,
            },
          ],
        })
        .expect(201);

      const orderId = createOrderResponse.body.data._id;
      expect(createOrderResponse.body.data.status).toBe("pending");

      // Step 2: Confirm order (waiter)
      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({ status: "confirmed" })
        .expect(200);

      // Step 3: Start preparation (kitchen staff)
      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${kitchenToken}`)
        .send({ status: "inprogress" })
        .expect(200);

      // Step 4: Mark as ready (kitchen staff)
      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${kitchenToken}`)
        .send({ status: "ready" })
        .expect(200);

      // Step 5: Mark as served (waiter)
      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({ status: "served" })
        .expect(200);

      // Step 6: Process payment (admin or cashier)
      const paymentResponse = await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${adminToken}`)
        .send({
          status: "paid",
          paidAmount: 31.98, // 2 * 15.99
          paymentType: "cash",
        })
        .expect(200);

      expect(paymentResponse.body.data.status).toBe("paid");

      // Verify final order state
      const finalOrder = await Order.findById(orderId);
      expect(finalOrder).toBeTruthy();
      expect(finalOrder!.status).toBe("paid");
      expect(finalOrder!.paidAmount).toBe(31.98);

      // Verify table is released
      const updatedTable = await Table.findById(table._id);
      expect(updatedTable).toBeTruthy();
      expect(updatedTable!.status).toBe("available");

      // Verify stock was consumed
      const updatedIngredient = await Item.findById(ingredient._id);
      expect(updatedIngredient).toBeTruthy();
      expect(updatedIngredient!.remainingStock).toBe(99.6); // 100 - (2 * 0.2)
      expect(updatedIngredient!.usedStock).toBe(0.4);
    });

    test("should prevent invalid state transitions", async () => {
      // Create order
      const createOrderResponse = await request(app)
        .post("/api/order")
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({
          orderType: "dine_in",
          menuItems: [
            {
              item: menuItem._id,
              quantity: 1,
            },
          ],
        })
        .expect(201);

      const orderId = createOrderResponse.body.data._id;

      // Try to jump from pending to served (invalid transition)
      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({ status: "served" })
        .expect(400);

      // Try to mark as paid without payment amount
      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${adminToken}`)
        .send({ status: "paid" })
        .expect(400);
    });

    test("should enforce role-based permissions for state changes", async () => {
      // Create and confirm order
      const createOrderResponse = await request(app)
        .post("/api/order")
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({
          orderType: "dine_in",
          menuItems: [
            {
              item: menuItem._id,
              quantity: 1,
            },
          ],
        })
        .expect(201);

      const orderId = createOrderResponse.body.data._id;

      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({ status: "confirmed" })
        .expect(200);

      // Waiter should not be able to mark as in progress (kitchen staff only)
      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({ status: "inprogress" })
        .expect(403);

      // Kitchen staff should be able to mark as in progress
      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${kitchenToken}`)
        .send({ status: "inprogress" })
        .expect(200);
    });
  });

  describe("Order State Machine Integration", () => {
    test("should validate state transitions using state machine", async () => {
      const currentState = OrderState.PENDING;
      const newState = OrderState.SERVED;

      const canTransition = OrderStateMachine.canTransition(
        currentState,
        newState
      );
      expect(canTransition).toBe(false);

      const validation = OrderStateMachine.validateTransition(
        {
          status: currentState,
          menuItems: [{ item: menuItem._id, quantity: 1 }],
        },
        newState,
        { userRole: "waiter" }
      );

      expect(validation.valid).toBe(false);
      expect(validation.error).toContain("Invalid transition");
    });

    test("should get next possible states for user role", async () => {
      const nextStates = OrderStateMachine.getNextStates(
        OrderState.READY,
        "waiter"
      );
      expect(nextStates).toContain(OrderState.SERVED);
      expect(nextStates).toContain(OrderState.COMPLAINT);
      expect(nextStates).not.toContain(OrderState.IN_PROGRESS);
    });

    test("should calculate progress percentage", async () => {
      const progress = OrderStateMachine.getProgressPercentage(
        OrderState.IN_PROGRESS
      );
      expect(progress).toBeGreaterThan(0);
      expect(progress).toBeLessThan(100);

      const completedProgress = OrderStateMachine.getProgressPercentage(
        OrderState.PAID
      );
      expect(completedProgress).toBe(100);
    });
  });

  describe("Stock Integration with Orders", () => {
    test("should consume stock when order is confirmed", async () => {
      const initialStock = ingredient.remainingStock;

      // Create and confirm order
      const createOrderResponse = await request(app)
        .post("/api/order")
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({
          orderType: "dine_in",
          menuItems: [
            {
              item: menuItem._id,
              quantity: 3, // 3 burgers = 3 * 0.2kg = 0.6kg
            },
          ],
        })
        .expect(201);

      const orderId = createOrderResponse.body.data._id;

      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({ status: "confirmed" })
        .expect(200);

      // Check stock was consumed
      const updatedIngredient = await Item.findById(ingredient._id);
      expect(updatedIngredient).toBeTruthy();
      expect(updatedIngredient!.remainingStock).toBe(initialStock - 0.6);
      expect(updatedIngredient!.usedStock).toBe(0.6);
    });

    test("should prevent order confirmation with insufficient stock", async () => {
      // Reduce stock to insufficient level
      ingredient.remainingStock = 0.1; // Less than required 0.2kg
      await ingredient.save();

      // Try to create order
      const response = await request(app)
        .post("/api/order")
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({
          orderType: "dine_in",
          menuItems: [
            {
              item: menuItem._id,
              quantity: 1,
            },
          ],
        })
        .expect(409); // Conflict due to insufficient stock

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("stock");
    });
  });

  describe("Table Management Integration", () => {
    test("should reserve and release tables correctly", async () => {
      expect(table.status).toBe("available");

      // Create order with table
      const createOrderResponse = await request(app)
        .post("/api/order")
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({
          orderType: "dine_in",
          table: [table._id],
          menuItems: [
            {
              item: menuItem._id,
              quantity: 1,
            },
          ],
        })
        .expect(201);

      // Table should be reserved
      const reservedTable = await Table.findById(table._id);
      expect(reservedTable).toBeTruthy();
      expect(reservedTable!.status).toBe("seated");

      // Complete order workflow to payment
      const orderId = createOrderResponse.body.data._id;

      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({ status: "confirmed" })
        .expect(200);

      await request(app)
        .patch(`/api/order/${orderId}`)
        .set("Authorization", `Bearer ${adminToken}`)
        .send({
          status: "paid",
          paidAmount: 15.99,
          paymentType: "cash",
        })
        .expect(200);

      // Table should be released
      const releasedTable = await Table.findById(table._id);
      expect(releasedTable).toBeTruthy();
      expect(releasedTable!.status).toBe("available");
    });

    test("should prevent double booking of tables", async () => {
      // Create first order with table
      await request(app)
        .post("/api/order")
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({
          orderType: "dine_in",
          table: [table._id],
          menuItems: [
            {
              item: menuItem._id,
              quantity: 1,
            },
          ],
        })
        .expect(201);

      // Try to create second order with same table
      const response = await request(app)
        .post("/api/order")
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({
          orderType: "dine_in",
          table: [table._id],
          menuItems: [
            {
              item: menuItem._id,
              quantity: 1,
            },
          ],
        })
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("not available");
    });
  });

  describe("Error Handling Integration", () => {
    test("should handle database errors gracefully", async () => {
      // Try to create order with invalid menu item
      const response = await request(app)
        .post("/api/order")
        .set("Authorization", `Bearer ${waiterToken}`)
        .send({
          orderType: "dine_in",
          menuItems: [
            {
              item: new mongoose.Types.ObjectId(), // Non-existent item
              quantity: 1,
            },
          ],
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBeDefined();
    });

    test("should rollback transactions on failure", async () => {
      const initialStock = ingredient.remainingStock;

      // Mock a failure during order processing
      const originalSave = Order.prototype.save;
      Order.prototype.save = jest
        .fn()
        .mockRejectedValue(new Error("Database error"));

      try {
        await request(app)
          .post("/api/order")
          .set("Authorization", `Bearer ${waiterToken}`)
          .send({
            orderType: "dine_in",
            menuItems: [
              {
                item: menuItem._id,
                quantity: 1,
              },
            ],
          })
          .expect(500);

        // Stock should not be consumed due to rollback
        const unchangedIngredient = await Item.findById(ingredient._id);
        expect(unchangedIngredient).toBeTruthy();
        expect(unchangedIngredient!.remainingStock).toBe(initialStock);
      } finally {
        // Restore original method
        Order.prototype.save = originalSave;
      }
    });
  });
});
