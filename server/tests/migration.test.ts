import mongoose from 'mongoose';
import { User } from '../api/models/user.model';
import RestaurantModel from '../api/models/restaurant.model';
import RestaurantOwnership from '../api/models/restaurantOwnership.model';
import Franchise from '../api/models/franchise.model';
import { 
  runMultiTenantMigration, 
  checkMigrationStatus, 
  rollbackMultiTenantMigration 
} from '../migrations/001_multi_tenant_migration';
import constants from '../api/constants';

describe('Database Migration Tests', () => {
  beforeAll(async () => {
    const testDbUrl = process.env.MONGO_TEST || 'mongodb://localhost:27017/restaurant_test';

    // Safety check to prevent using production database
    if (testDbUrl.includes('cluster0.awfwbid.mongodb.net') ||
        testDbUrl.includes('cluster0.prwg7.mongodb.net') ||
        !testDbUrl.includes('test')) {
      throw new Error('DANGER: Tests are configured to use production database!');
    }

    await mongoose.connect(testDbUrl);
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clean up all collections
    await User.deleteMany({});
    await RestaurantModel.deleteMany({});
    await RestaurantOwnership.deleteMany({});
    await Franchise.deleteMany({});
  });

  describe('Migration Status Check', () => {
    it('should detect unmigrated state', async () => {
      // Create old-style data without new fields
      await User.create({
        name: 'Old User',
        email: '<EMAIL>',
        password: 'password123',
        userRole: constants.USERROLE.ADMIN,
        isSuperAdmin: false,
        gender: 'male',
      });

      await RestaurantModel.create({
        name: 'Old Restaurant',
        logo: 'old-logo.jpg',
        address: '123 Old St',
        phone: '+1234567890',
        description: 'Old restaurant',
        subStart: new Date(),
        subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      });

      const status = await checkMigrationStatus();
      expect(status.isMigrated).toBe(false);
      expect(status.stats.restaurantsWithNewFields).toBe(0);
      expect(status.stats.usersWithNewFields).toBe(0);
      expect(status.stats.totalOwnerships).toBe(0);
    });

    it('should detect migrated state', async () => {
      // Create new-style data with new fields
      await User.create({
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
        userRole: constants.USERROLE.FRANCHISE_OWNER,
        isSuperAdmin: false,
        isActive: true,
        ownedRestaurants: [],
        loginCount: 0,
      });

      await RestaurantModel.create({
        name: 'New Restaurant',
        logo: 'new-logo.jpg',
        address: '123 New St',
        phone: '+1234567890',
        description: 'New restaurant',
        subStart: new Date(),
        subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        isActive: true,
        restaurantType: 'independent',
      });

      const status = await checkMigrationStatus();
      expect(status.isMigrated).toBe(true);
      expect(status.stats.restaurantsWithNewFields).toBe(1);
      expect(status.stats.usersWithNewFields).toBe(1);
    });
  });

  describe('Migration Execution', () => {
    it('should successfully migrate existing data', async () => {
      // Create old-style data
      const oldRestaurant = await RestaurantModel.create({
        name: 'Old Restaurant',
        logo: 'old-logo.jpg',
        address: '123 Old St',
        phone: '+1234567890',
        description: 'Old restaurant',
        subStart: new Date(),
        subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      });

      const oldAdmin = await User.create({
        name: 'Old Admin',
        email: '<EMAIL>',
        password: 'password123',
        userRole: constants.USERROLE.ADMIN,
        restaurant: oldRestaurant._id,
        isSuperAdmin: false,
        joinDate: new Date(),
      });

      // Run migration
      const result = await runMultiTenantMigration();

      expect(result.success).toBe(true);
      expect(result.stats.restaurantsUpdated).toBeGreaterThan(0);
      expect(result.stats.usersUpdated).toBeGreaterThan(0);
      expect(result.stats.ownershipsCreated).toBeGreaterThan(0);

      // Verify migration results
      const migratedRestaurant = await RestaurantModel.findById(oldRestaurant._id);
      expect(migratedRestaurant?.isActive).toBe(true);
      expect(migratedRestaurant?.restaurantType).toBe('independent');
      expect(migratedRestaurant?.currentOwner?.toString()).toBe(oldAdmin._id?.toString());

      const migratedUser = await User.findById(oldAdmin._id);
      expect(migratedUser?.isActive).toBe(true);
      expect(migratedUser?.ownedRestaurants).toBeDefined();
      expect(migratedUser?.loginCount).toBe(0);

      // Verify ownership record was created
      const ownership = await RestaurantOwnership.findOne({
        owner: oldAdmin._id,
        restaurant: oldRestaurant._id,
      });
      expect(ownership).toBeTruthy();
      expect(ownership?.ownershipType).toBe('full');
      expect(ownership?.ownershipPercentage).toBe(100);
      expect(ownership?.isActive).toBe(true);
    });

    it('should handle multiple admins with restaurants', async () => {
      // Create multiple restaurants and admins
      const restaurant1 = await RestaurantModel.create({
        name: 'Restaurant 1',
        logo: 'logo1.jpg',
        address: '123 St 1',
        phone: '+1111111111',
        description: 'Restaurant 1',
        subStart: new Date(),
        subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      });

      const restaurant2 = await RestaurantModel.create({
        name: 'Restaurant 2',
        logo: 'logo2.jpg',
        address: '123 St 2',
        phone: '+2222222222',
        description: 'Restaurant 2',
        subStart: new Date(),
        subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      });

      const admin1 = await User.create({
        name: 'Admin 1',
        email: '<EMAIL>',
        password: 'password123',
        userRole: constants.USERROLE.ADMIN,
        restaurant: restaurant1._id,
        isSuperAdmin: false,
      });

      const admin2 = await User.create({
        name: 'Admin 2',
        email: '<EMAIL>',
        password: 'password123',
        userRole: constants.USERROLE.ADMIN,
        restaurant: restaurant2._id,
        isSuperAdmin: false,
      });

      // Run migration
      const result = await runMultiTenantMigration();

      expect(result.success).toBe(true);
      expect(result.stats.ownershipsCreated).toBe(2);

      // Verify both ownerships were created
      const ownership1 = await RestaurantOwnership.findOne({
        owner: admin1._id,
        restaurant: restaurant1._id,
      });
      expect(ownership1).toBeTruthy();

      const ownership2 = await RestaurantOwnership.findOne({
        owner: admin2._id,
        restaurant: restaurant2._id,
      });
      expect(ownership2).toBeTruthy();

      // Verify restaurant owners were set
      const updatedRestaurant1 = await RestaurantModel.findById(restaurant1._id);
      expect(updatedRestaurant1?.currentOwner?.toString()).toBe(admin1._id?.toString());

      const updatedRestaurant2 = await RestaurantModel.findById(restaurant2._id);
      expect(updatedRestaurant2?.currentOwner?.toString()).toBe(admin2._id?.toString());
    });

    it('should skip already migrated data', async () => {
      // Create already migrated data
      const restaurant = await RestaurantModel.create({
        name: 'Migrated Restaurant',
        logo: 'migrated-logo.jpg',
        address: '123 Migrated St',
        phone: '+1234567890',
        description: 'Migrated restaurant',
        subStart: new Date(),
        subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        isActive: true,
        restaurantType: 'independent',
      });

      const user = await User.create({
        name: 'Migrated User',
        email: '<EMAIL>',
        password: 'password123',
        userRole: constants.USERROLE.ADMIN,
        restaurant: restaurant._id,
        isSuperAdmin: false,
        isActive: true,
        ownedRestaurants: [],
        loginCount: 0,
      });

      // Run migration
      const result = await runMultiTenantMigration();

      expect(result.success).toBe(true);
      // Should not create duplicate ownership records
      const ownerships = await RestaurantOwnership.find({
        owner: user._id,
        restaurant: restaurant._id,
      });
      expect(ownerships.length).toBeLessThanOrEqual(1);
    });

    it('should handle users without restaurants', async () => {
      // Create user without restaurant assignment
      await User.create({
        name: 'No Restaurant User',
        email: '<EMAIL>',
        password: 'password123',
        userRole: constants.USERROLE.USER,
        isSuperAdmin: false,
      });

      // Run migration
      const result = await runMultiTenantMigration();

      expect(result.success).toBe(true);
      // Should not create ownership records for users without restaurants
      const ownerships = await RestaurantOwnership.find({});
      expect(ownerships.length).toBe(0);
    });
  });

  describe('Migration Rollback', () => {
    it('should successfully rollback migration', async () => {
      // Create migrated data
      const restaurant = await RestaurantModel.create({
        name: 'Migrated Restaurant',
        logo: 'migrated-logo.jpg',
        address: '123 Migrated St',
        phone: '+1234567890',
        description: 'Migrated restaurant',
        subStart: new Date(),
        subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        isActive: true,
        restaurantType: 'franchise',
        currentOwner: new mongoose.Types.ObjectId(),
      });

      const user = await User.create({
        name: 'Migrated User',
        email: '<EMAIL>',
        password: 'password123',
        userRole: constants.USERROLE.FRANCHISE_OWNER,
        isSuperAdmin: false,
        isActive: true,
        ownedRestaurants: [restaurant._id],
        loginCount: 5,
      });

      await RestaurantOwnership.create({
        owner: user._id,
        restaurant: restaurant._id,
        ownershipType: 'full',
        ownershipPercentage: 100,
        isActive: true,
      });

      await Franchise.create({
        name: 'Test Franchise',
        brandName: 'Test Brand',
        owner: user._id,
        isActive: true,
      });

      // Run rollback
      const result = await rollbackMultiTenantMigration();

      expect(result.success).toBe(true);

      // Verify rollback results
      const rolledBackRestaurant = await RestaurantModel.findById(restaurant._id);
      expect(rolledBackRestaurant?.currentOwner).toBeUndefined();
      expect(rolledBackRestaurant?.franchise).toBeUndefined();
      expect(rolledBackRestaurant?.restaurantType).toBeUndefined();
      expect(rolledBackRestaurant?.isActive).toBeUndefined();

      const rolledBackUser = await User.findById(user._id);
      expect(rolledBackUser?.ownedRestaurants).toBeUndefined();
      expect(rolledBackUser?.franchise).toBeUndefined();
      expect(rolledBackUser?.isActive).toBeUndefined();
      expect(rolledBackUser?.loginCount).toBeUndefined();

      // Verify ownership and franchise records are removed
      const ownerships = await RestaurantOwnership.find({});
      expect(ownerships.length).toBe(0);

      const franchises = await Franchise.find({});
      expect(franchises.length).toBe(0);
    });
  });

  describe('Migration Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // Close connection to simulate error
      await mongoose.connection.close();

      try {
        await runMultiTenantMigration();
        fail('Should have thrown an error');
      } catch (error) {
        expect(error).toBeDefined();
      }

      // Reconnect for cleanup
      await mongoose.connect(process.env.MONGO_TEST || 'mongodb://localhost:27017/restaurant_test');
    });

    it('should handle invalid data gracefully', async () => {
      // Create invalid data that might cause migration issues
      await RestaurantModel.create({
        name: 'Invalid Restaurant',
        logo: 'invalid-logo.jpg',
        address: '123 Invalid St',
        phone: 'invalid-phone', // Invalid phone format
        description: 'Invalid restaurant',
        subStart: new Date(),
        subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      });

      // Migration should still succeed despite invalid data
      const result = await runMultiTenantMigration();
      expect(result.success).toBe(true);
    });
  });

  describe('Migration Performance', () => {
    it('should handle large datasets efficiently', async () => {
      // Create a larger dataset to test performance
      const restaurants = [];
      const users = [];

      for (let i = 0; i < 50; i++) {
        const restaurant = await RestaurantModel.create({
          name: `Restaurant ${i}`,
          logo: `logo${i}.jpg`,
          address: `123 Street ${i}`,
          phone: `+123456789${i}`,
          description: `Restaurant ${i}`,
          subStart: new Date(),
          subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        });
        restaurants.push(restaurant);

        const user = await User.create({
          name: `Admin ${i}`,
          email: `admin${i}@test.com`,
          password: 'password123',
          userRole: constants.USERROLE.ADMIN,
          restaurant: restaurant._id,
          isSuperAdmin: false,
        });
        users.push(user);
      }

      const startTime = Date.now();
      const result = await runMultiTenantMigration();
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.stats.restaurantsUpdated).toBe(50);
      expect(result.stats.usersUpdated).toBe(50);
      expect(result.stats.ownershipsCreated).toBe(50);

      // Migration should complete in reasonable time (less than 10 seconds for 50 records)
      expect(endTime - startTime).toBeLessThan(10000);
    });
  });
});
