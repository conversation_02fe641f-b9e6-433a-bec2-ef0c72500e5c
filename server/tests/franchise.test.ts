import request from 'supertest';
import mongoose from 'mongoose';
import { app } from '../app/app';
import { User } from '../api/models/user.model';
import RestaurantModel from '../api/models/restaurant.model';
import Franchise from '../api/models/franchise.model';
import constants from '../api/constants';

describe('Franchise Management Tests', () => {
  let superAdminToken: string;
  let franchiseOwnerToken: string;
  let adminToken: string;
  let testFranchise: any;
  let franchiseOwner: any;
  let testRestaurant: any;

  beforeAll(async () => {
    await mongoose.connect(process.env.MONGO_TEST || 'mongodb://localhost:27017/restaurant_test');
    
    // Clean up
    await User.deleteMany({});
    await RestaurantModel.deleteMany({});
    await Franchise.deleteMany({});
  });

  afterAll(async () => {
    await User.deleteMany({});
    await RestaurantModel.deleteMany({});
    await Franchise.deleteMany({});
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Create test users
    const superAdmin = await User.create({
      name: 'Super Admin',
      email: '<EMAIL>',
      password: 'password123',
      userRole: constants.USERROLE.SUPERADMIN,
      role:
      isSuperAdmin: true,
      isActive: true,
    });

    franchiseOwner = await User.create({
      name: 'Franchise Owner',
      email: '<EMAIL>',
      password: 'password123',
      userRole: constants.USERROLE.FRANCHISE_OWNER,
      role:
      isSuperAdmin: false,
      isActive: true,
    });

    const admin = await User.create({
      name: 'Restaurant Admin',
      email: '<EMAIL>',
      password: 'password123',
      userRole: constants.USERROLE.ADMIN,
      role:
      isSuperAdmin: false,
      isActive: true,
    });

    // Create test restaurant
    testRestaurant = await RestaurantModel.create({
      name: 'Test Restaurant',
      logo: 'test-logo.jpg',
      address: '123 Test St',
      phone: '+1234567890',
      description: 'Test restaurant',
      subStart: new Date(),
      subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      isActive: true,
      restaurantType: 'franchise',
      currentOwner: franchiseOwner._id,
    });

    // Get tokens
    const superAdminLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    superAdminToken = superAdminLogin.body.data.token;

    const franchiseOwnerLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    franchiseOwnerToken = franchiseOwnerLogin.body.data.token;

    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    adminToken = adminLogin.body.data.token;
  });

  afterEach(async () => {
    await User.deleteMany({});
    await RestaurantModel.deleteMany({});
    await Franchise.deleteMany({});
  });

  describe('Franchise Creation', () => {
    it('should create a franchise as super admin', async () => {
      const franchiseData = {
        name: 'Test Franchise',
        brandName: 'Test Brand',
        ownerId: franchiseOwner._id.toString(),
        description: 'Test franchise description',
        contactInfo: {
          email: '<EMAIL>',
          phone: '+1234567890',
        },
        headquarters: {
          address: '123 HQ Street',
          city: 'Test City',
          state: 'Test State',
          country: 'Test Country',
          zipCode: '12345',
        },
      };

      const response = await request(app)
        .post('/api/franchise')
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send(franchiseData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Test Franchise');
      expect(response.body.data.brandName).toBe('Test Brand');
      testFranchise = response.body.data;
    });

    it('should create a franchise as franchise owner', async () => {
      const franchiseData = {
        name: 'Owner Franchise',
        brandName: 'Owner Brand',
        ownerId: franchiseOwner._id.toString(),
        description: 'Franchise created by owner',
      };

      const response = await request(app)
        .post('/api/franchise')
        .set('Authorization', `Bearer ${franchiseOwnerToken}`)
        .send(franchiseData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Owner Franchise');
    });

    it('should reject franchise creation by non-franchise owner', async () => {
      const franchiseData = {
        name: 'Unauthorized Franchise',
        brandName: 'Unauthorized Brand',
        ownerId: franchiseOwner._id.toString(),
      };

      const response = await request(app)
        .post('/api/franchise')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(franchiseData);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    it('should reject franchise with duplicate name', async () => {
      // Create first franchise
      await Franchise.create({
        name: 'Duplicate Franchise',
        brandName: 'Duplicate Brand',
        owner: franchiseOwner._id,
        isActive: true,
      });

      const franchiseData = {
        name: 'Duplicate Franchise',
        brandName: 'Different Brand',
        ownerId: franchiseOwner._id.toString(),
      };

      const response = await request(app)
        .post('/api/franchise')
        .set('Authorization', `Bearer ${franchiseOwnerToken}`)
        .send(franchiseData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('already exists');
    });
  });

  describe('Franchise Retrieval', () => {
    beforeEach(async () => {
      testFranchise = await Franchise.create({
        name: 'Test Franchise',
        brandName: 'Test Brand',
        owner: franchiseOwner._id,
        description: 'Test franchise',
        isActive: true,
        restaurants: [testRestaurant._id],
      });
    });

    it('should get all franchises as super admin', async () => {
      const response = await request(app)
        .get('/api/franchise')
        .set('Authorization', `Bearer ${superAdminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('should get franchise by ID', async () => {
      const response = await request(app)
        .get(`/api/franchise/${testFranchise._id}`)
        .set('Authorization', `Bearer ${franchiseOwnerToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Test Franchise');
    });

    it('should get franchises by owner', async () => {
      const response = await request(app)
        .get(`/api/franchise/owner/${franchiseOwner._id}`)
        .set('Authorization', `Bearer ${franchiseOwnerToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(1);
    });

    it('should deny access to other users franchise', async () => {
      const response = await request(app)
        .get(`/api/franchise/${testFranchise._id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Franchise Updates', () => {
    beforeEach(async () => {
      testFranchise = await Franchise.create({
        name: 'Test Franchise',
        brandName: 'Test Brand',
        owner: franchiseOwner._id,
        description: 'Test franchise',
        isActive: true,
      });
    });

    it('should update franchise as owner', async () => {
      const updateData = {
        description: 'Updated franchise description',
        contactInfo: {
          email: '<EMAIL>',
          phone: '+9876543210',
        },
      };

      const response = await request(app)
        .put(`/api/franchise/${testFranchise._id}`)
        .set('Authorization', `Bearer ${franchiseOwnerToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.description).toBe('Updated franchise description');
    });

    it('should deny update by non-owner', async () => {
      const updateData = {
        description: 'Unauthorized update',
      };

      const response = await request(app)
        .put(`/api/franchise/${testFranchise._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Restaurant Association', () => {
    beforeEach(async () => {
      testFranchise = await Franchise.create({
        name: 'Test Franchise',
        brandName: 'Test Brand',
        owner: franchiseOwner._id,
        description: 'Test franchise',
        isActive: true,
      });
    });

    it('should add restaurant to franchise', async () => {
      const response = await request(app)
        .post(`/api/franchise/${testFranchise._id}/restaurants`)
        .set('Authorization', `Bearer ${franchiseOwnerToken}`)
        .send({ restaurantId: testRestaurant._id.toString() });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.restaurants).toContain(testRestaurant._id.toString());
    });

    it('should remove restaurant from franchise', async () => {
      // First add restaurant
      await testFranchise.addRestaurant(testRestaurant._id.toString());

      const response = await request(app)
        .delete(`/api/franchise/${testFranchise._id}/restaurants/${testRestaurant._id}`)
        .set('Authorization', `Bearer ${franchiseOwnerToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should deny restaurant association by non-owner', async () => {
      const response = await request(app)
        .post(`/api/franchise/${testFranchise._id}/restaurants`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ restaurantId: testRestaurant._id.toString() });

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Franchise Deletion', () => {
    beforeEach(async () => {
      testFranchise = await Franchise.create({
        name: 'Test Franchise',
        brandName: 'Test Brand',
        owner: franchiseOwner._id,
        description: 'Test franchise',
        isActive: true,
        restaurants: [testRestaurant._id],
      });
    });

    it('should deactivate franchise as owner', async () => {
      const response = await request(app)
        .delete(`/api/franchise/${testFranchise._id}`)
        .set('Authorization', `Bearer ${franchiseOwnerToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify franchise is deactivated
      const franchise = await Franchise.findById(testFranchise._id);
      expect(franchise?.isActive).toBe(false);
    });

    it('should deny deletion by non-owner', async () => {
      const response = await request(app)
        .delete(`/api/franchise/${testFranchise._id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });
});
