import { Response } from "express";
import { AuthenticatedRequest } from "../types/common.types";
import { formatResponse } from "../utilities/formatRes";
import RestaurantOwnership from "../models/restaurantOwnership.model";
import { User } from "../models/user.model";
import RestaurantModel from "../models/restaurant.model";
import Logger from "../../utils/logUtils";
import constants from "../constants";
import { z } from "zod";

// Validation schemas
const createOwnershipSchema = z.object({
  ownerId: z.string().regex(/^[0-9a-fA-F]{24}$/, "Invalid owner ID"),
  restaurantId: z.string().regex(/^[0-9a-fA-F]{24}$/, "Invalid restaurant ID"),
  ownershipType: z.enum(["full", "partial", "franchise"]).optional(),
  ownershipPercentage: z.number().min(0).max(100).optional(),
  startDate: z.coerce.date().optional(),
  notes: z.string().optional(),
});

const transferOwnershipSchema = z.object({
  currentOwnerId: z
    .string()
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid current owner ID"),
  newOwnerId: z.string().regex(/^[0-9a-fA-F]{24}$/, "Invalid new owner ID"),
  restaurantId: z.string().regex(/^[0-9a-fA-F]{24}$/, "Invalid restaurant ID"),
  transferDate: z.coerce.date().optional(),
  notes: z.string().optional(),
});

const RestaurantOwnershipController = {
  // Create new restaurant ownership
  create: async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Only super admin and franchise owners can create ownership
      if (
        !req.user?.isSuperAdmin &&
        req.user?.userRole !== constants.USERROLE.FRANCHISE_OWNER
      ) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const data = await createOwnershipSchema.parseAsync(req.body);

      // Verify owner exists and has appropriate role
      const owner = await User.findById(data.ownerId);
      if (!owner) {
        return formatResponse(res, 404, false, "Owner not found");
      }

      if (
        ![
          constants.USERROLE.FRANCHISE_OWNER,
          constants.USERROLE.ADMIN,
        ].includes(owner.userRole)
      ) {
        return formatResponse(
          res,
          400,
          false,
          "User cannot be an owner with this role"
        );
      }

      // Verify restaurant exists
      const restaurant = await RestaurantModel.findById(data.restaurantId);
      if (!restaurant) {
        return formatResponse(res, 404, false, "Restaurant not found");
      }

      // Check if ownership already exists
      const existingOwnership = await RestaurantOwnership.findOne({
        owner: data.ownerId,
        restaurant: data.restaurantId,
        isActive: true,
      });

      if (existingOwnership) {
        return formatResponse(
          res,
          400,
          false,
          "Ownership already exists for this restaurant"
        );
      }

      // Create ownership
      const ownership = new RestaurantOwnership({
        owner: data.ownerId,
        restaurant: data.restaurantId,
        ownershipType: data.ownershipType || "full",
        ownershipPercentage: data.ownershipPercentage || 100,
        startDate: data.startDate || new Date(),
        notes: data.notes,
      });

      await ownership.save();

      // Update restaurant's current owner
      restaurant.currentOwner = data.ownerId as any;
      await restaurant.save();

      // Add restaurant to user's owned restaurants if franchise owner
      if (owner.userRole === constants.USERROLE.FRANCHISE_OWNER) {
        await owner.addOwnedRestaurant(data.restaurantId);
      }

      const populatedOwnership = await RestaurantOwnership.findById(
        ownership._id
      )
        .populate("owner", "name email userRole")
        .populate("restaurant", "name address phone");

      formatResponse(
        res,
        201,
        true,
        "Restaurant ownership created successfully",
        populatedOwnership
      );
    } catch (error) {
      Logger.error("Error creating restaurant ownership:", error);
      formatResponse(
        res,
        500,
        false,
        "Error creating restaurant ownership",
        error
      );
    }
  },

  // Get all ownerships (super admin only)
  getAll: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user?.isSuperAdmin) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const ownerships = await RestaurantOwnership.find({ isActive: true })
        .populate("owner", "name email userRole")
        .populate("restaurant", "name address phone")
        .populate("transferredFrom", "name email")
        .populate("transferredTo", "name email");

      formatResponse(
        res,
        200,
        true,
        "Restaurant ownerships retrieved successfully",
        ownerships
      );
    } catch (error) {
      Logger.error("Error fetching restaurant ownerships:", error);
      formatResponse(
        res,
        500,
        false,
        "Error fetching restaurant ownerships",
        error
      );
    }
  },

  // Get ownerships for a specific owner
  getByOwner: async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { ownerId } = req.params;

      // Users can only view their own ownerships unless they're super admin
      if (!req.user?.isSuperAdmin && req.user?.id !== ownerId) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const ownerships = await RestaurantOwnership.getActiveRestaurantsForOwner(
        ownerId
      );

      formatResponse(
        res,
        200,
        true,
        "Owner's restaurants retrieved successfully",
        ownerships
      );
    } catch (error) {
      Logger.error("Error fetching owner's restaurants:", error);
      formatResponse(
        res,
        500,
        false,
        "Error fetching owner's restaurants",
        error
      );
    }
  },

  // Get ownerships for a specific restaurant
  getByRestaurant: async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { restaurantId } = req.params;

      // Check if user has access to this restaurant
      if (!req.user?.isSuperAdmin) {
        const hasAccess =
          req.user?.restaurant?.toString() === restaurantId ||
          req.user?.ownedRestaurants?.some(
            (r: any) => r.toString() === restaurantId
          );

        if (!hasAccess) {
          return formatResponse(res, 403, false, "Access denied");
        }
      }

      const ownerships = await RestaurantOwnership.getActiveOwnersForRestaurant(
        restaurantId
      );

      formatResponse(
        res,
        200,
        true,
        "Restaurant owners retrieved successfully",
        ownerships
      );
    } catch (error) {
      Logger.error("Error fetching restaurant owners:", error);
      formatResponse(
        res,
        500,
        false,
        "Error fetching restaurant owners",
        error
      );
    }
  },

  // Transfer ownership
  transferOwnership: async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Only super admin and current owners can transfer ownership
      if (
        !req.user?.isSuperAdmin &&
        req.user?.userRole !== constants.USERROLE.FRANCHISE_OWNER
      ) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const data = await transferOwnershipSchema.parseAsync(req.body);

      // Verify current ownership
      const currentOwnership = await RestaurantOwnership.findOne({
        owner: data.currentOwnerId,
        restaurant: data.restaurantId,
        isActive: true,
      });

      if (!currentOwnership) {
        return formatResponse(res, 404, false, "Current ownership not found");
      }

      // SECURITY FIX: Ensure the requesting user actually owns the restaurant they're trying to transfer
      // Only super admin can transfer any restaurant, franchise owners can only transfer their own
      if (!req.user?.isSuperAdmin) {
        if (data.currentOwnerId !== req.user.id) {
          return formatResponse(
            res,
            403,
            false,
            "You can only transfer restaurants that you own"
          );
        }

        // Double-check that the user actually owns this restaurant
        const userOwnership = await RestaurantOwnership.findOne({
          owner: req.user.id,
          restaurant: data.restaurantId,
          isActive: true,
        });

        if (!userOwnership) {
          return formatResponse(
            res,
            403,
            false,
            "You do not own this restaurant"
          );
        }
      }

      // Verify new owner exists and has appropriate role
      const newOwner = await User.findById(data.newOwnerId);
      if (!newOwner) {
        return formatResponse(res, 404, false, "New owner not found");
      }

      if (
        ![
          constants.USERROLE.FRANCHISE_OWNER,
          constants.USERROLE.ADMIN,
        ].includes(newOwner.userRole)
      ) {
        return formatResponse(
          res,
          400,
          false,
          "New owner cannot have ownership with this role"
        );
      }

      // Transfer ownership
      await currentOwnership.transferOwnership(data.newOwnerId, data.notes);

      // Create new ownership record
      const newOwnership = new RestaurantOwnership({
        owner: data.newOwnerId,
        restaurant: data.restaurantId,
        ownershipType: currentOwnership.ownershipType,
        ownershipPercentage: currentOwnership.ownershipPercentage,
        startDate: data.transferDate || new Date(),
        transferredFrom: data.currentOwnerId,
        notes: data.notes,
      });

      await newOwnership.save();

      // Update restaurant's current owner
      const restaurant = await RestaurantModel.findById(data.restaurantId);
      if (restaurant) {
        restaurant.currentOwner = data.newOwnerId as any;
        await restaurant.save();
      }

      // Update user's owned restaurants
      const currentOwner = await User.findById(data.currentOwnerId);
      if (
        currentOwner &&
        currentOwner.userRole === constants.USERROLE.FRANCHISE_OWNER
      ) {
        await currentOwner.removeOwnedRestaurant(data.restaurantId);
      }

      if (newOwner.userRole === constants.USERROLE.FRANCHISE_OWNER) {
        await newOwner.addOwnedRestaurant(data.restaurantId);
      }

      const populatedOwnership = await RestaurantOwnership.findById(
        newOwnership._id
      )
        .populate("owner", "name email userRole")
        .populate("restaurant", "name address phone")
        .populate("transferredFrom", "name email");

      formatResponse(
        res,
        200,
        true,
        "Ownership transferred successfully",
        populatedOwnership
      );
    } catch (error) {
      Logger.error("Error transferring ownership:", error);
      formatResponse(res, 500, false, "Error transferring ownership", error);
    }
  },

  // Delete ownership (deactivate)
  delete: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user?.isSuperAdmin) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const { id } = req.params;

      const ownership = await RestaurantOwnership.findById(id);
      if (!ownership) {
        return formatResponse(res, 404, false, "Ownership not found");
      }

      ownership.isActive = false;
      ownership.endDate = new Date();
      await ownership.save();

      // Remove from user's owned restaurants
      const owner = await User.findById(ownership.owner);
      if (owner && owner.userRole === constants.USERROLE.FRANCHISE_OWNER) {
        await owner.removeOwnedRestaurant(ownership.restaurant.toString());
      }

      // Clear restaurant's current owner
      const restaurant = await RestaurantModel.findById(ownership.restaurant);
      if (restaurant) {
        restaurant.currentOwner = undefined;
        await restaurant.save();
      }

      formatResponse(res, 200, true, "Ownership deactivated successfully");
    } catch (error) {
      Logger.error("Error deactivating ownership:", error);
      formatResponse(res, 500, false, "Error deactivating ownership", error);
    }
  },
};

export default RestaurantOwnershipController;
