import { Response, NextFunction } from "express";
import { AuthenticatedRequest } from "../types/common.types";
import { formatResponse } from "../utilities/formatRes";
import constants from "../constants";
import Logger from "../../utils/logUtils";
import RestaurantOwnership from "../models/restaurantOwnership.model";

/**
 * Data scoping middleware to ensure users only access their own data
 * This middleware adds restaurant filtering based on user role and permissions
 */

export const dataScopingMiddleware = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return formatResponse(res, 401, false, "Authentication required");
    }

    // Super admin can access all data - no scoping needed
    if (req.user.isSuperAdmin) {
      return next();
    }

    // Add restaurant scoping based on user role
    const userRole = req.user.userRole;
    const userId = req.user.id;

    switch (userRole) {
      case constants.USERROLE.FRANCHISE_OWNER:
        // Franchise owners can access data from all their owned restaurants
        if (req.user.ownedRestaurants && req.user.ownedRestaurants.length > 0) {
          req.scopedRestaurants = req.user.ownedRestaurants.map((r) =>
            r.toString()
          );
        } else {
          return formatResponse(
            res,
            403,
            false,
            "No restaurants found for this franchise owner"
          );
        }
        break;

      case constants.USERROLE.ADMIN:
        // Restaurant admins can only access their restaurant's data
        if (req.user.restaurant) {
          req.scopedRestaurants = [req.user.restaurant.toString()];
        } else {
          return formatResponse(
            res,
            403,
            false,
            "No restaurant assigned to this admin"
          );
        }
        break;

      case constants.USERROLE.USER:
        // Regular users can only access their restaurant's data
        if (req.user.restaurant) {
          req.scopedRestaurants = [req.user.restaurant.toString()];
        } else {
          return formatResponse(
            res,
            403,
            false,
            "No restaurant assigned to this user"
          );
        }
        break;

      default:
        return formatResponse(res, 403, false, "Invalid user role");
    }

    // Add scoped restaurant IDs to request for use in controllers
    req.user.scopedRestaurants = req.scopedRestaurants;

    next();
  } catch (error) {
    Logger.error("Data scoping middleware error:", error);
    return formatResponse(res, 500, false, "Error in data scoping", error);
  }
};

/**
 * Middleware to validate restaurant access for specific operations
 * Use this when a specific restaurant ID is provided in the request
 */
export const validateRestaurantAccess = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return formatResponse(res, 401, false, "Authentication required");
    }

    // Super admin can access any restaurant
    if (req.user.isSuperAdmin) {
      return next();
    }

    // Get restaurant ID from params, body, or query
    const restaurantId =
      req.params.restaurantId || req.body.restaurant || req.query.restaurant;

    if (!restaurantId) {
      return formatResponse(res, 400, false, "Restaurant ID is required");
    }

    // Check if user has access to this restaurant
    const hasAccess =
      req.user.scopedRestaurants?.includes(restaurantId) ||
      req.user.restaurant?.toString() === restaurantId ||
      req.user.ownedRestaurants?.some((r) => r.toString() === restaurantId);

    if (!hasAccess) {
      return formatResponse(
        res,
        403,
        false,
        "Access denied to this restaurant"
      );
    }

    next();
  } catch (error) {
    Logger.error("Restaurant access validation error:", error);
    return formatResponse(
      res,
      500,
      false,
      "Error validating restaurant access",
      error
    );
  }
};

/**
 * Middleware to add restaurant filter to database queries
 * This automatically adds restaurant filtering to find operations
 */
export const addRestaurantFilter = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return formatResponse(res, 401, false, "Authentication required");
    }

    // Super admin doesn't need filtering
    if (req.user.isSuperAdmin) {
      return next();
    }

    // Add restaurant filter based on user's access
    if (req.user.scopedRestaurants && req.user.scopedRestaurants.length > 0) {
      if (req.user.scopedRestaurants.length === 1) {
        req.restaurantFilter = { restaurant: req.user.scopedRestaurants[0] };
      } else {
        req.restaurantFilter = {
          restaurant: { $in: req.user.scopedRestaurants },
        };
      }
    } else if (req.user.restaurant) {
      req.restaurantFilter = { restaurant: req.user.restaurant.toString() };
    } else {
      return formatResponse(res, 403, false, "No restaurant access found");
    }

    next();
  } catch (error) {
    Logger.error("Restaurant filter middleware error:", error);
    return formatResponse(
      res,
      500,
      false,
      "Error adding restaurant filter",
      error
    );
  }
};

/**
 * Middleware specifically for franchise owner operations
 */
export const franchiseOwnerOnly = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return formatResponse(res, 401, false, "Authentication required");
    }

    if (
      !req.user.isSuperAdmin &&
      req.user.userRole !== constants.USERROLE.FRANCHISE_OWNER
    ) {
      console.log(req.user);
      return formatResponse(res, 403, false, "Franchise owner access required");
    }

    next();
  } catch (error) {
    Logger.error("Franchise owner middleware error:", error);
    return formatResponse(
      res,
      500,
      false,
      "Error validating franchise owner access",
      error
    );
  }
};

/**
 * Middleware to validate franchise access
 */
export const validateFranchiseAccess = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return formatResponse(res, 401, false, "Authentication required");
    }

    // Super admin can access any franchise
    if (req.user.isSuperAdmin) {
      return next();
    }

    // Get franchise ID from params, body, or query
    const franchiseId =
      req.params.franchiseId || req.body.franchise || req.query.franchise;

    if (!franchiseId) {
      return formatResponse(res, 400, false, "Franchise ID is required");
    }

    // Check if user has access to this franchise
    const hasAccess = req.user.franchise === franchiseId;

    if (!hasAccess) {
      return formatResponse(res, 403, false, "Access denied to this franchise");
    }

    next();
  } catch (error) {
    Logger.error("Franchise access validation error:", error);
    return formatResponse(
      res,
      500,
      false,
      "Error validating franchise access",
      error
    );
  }
};

// Extend the AuthenticatedRequest interface to include scoping fields
declare global {
  namespace Express {
    interface Request {
      scopedRestaurants?: string[];
      restaurantFilter?: any;
    }
  }
}

export default {
  dataScopingMiddleware,
  validateRestaurantAccess,
  addRestaurantFilter,
  franchiseOwnerOnly,
  validateFranchiseAccess,
};
