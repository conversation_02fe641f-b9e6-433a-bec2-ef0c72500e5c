import { AuthenticatedRequest } from './../types/common.types';
import jwt from "jsonwebtoken";
import { Request, Response, NextFunction } from "express";
import constants from "../constants";
import { formatResponse } from "../utilities/formatRes";
import { IUser } from '../types/user.types';



//* Middleware to protect routes
const authenticate = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  const token = req.headers.authorization?.split(" ")[1];

  if (!token) {
    formatResponse(res, 401, false, "Authentication required.");
    return;
  }

  try {
    const decoded = jwt.verify(token, constants.JWT_SECRET) as Partial<IUser>;
    req.user = decoded;
    next();
  } catch (error:any) {
    // Handle different JWT errors with appropriate status codes
    if (error.name === 'JsonWebTokenError') {
      formatResponse(res, 400, false, "Invalid token format");
    } else if (error.name === 'TokenExpiredError') {
      formatResponse(res, 401, false, "Token expired");
    } else if (error.name === 'NotBeforeError') {
      formatResponse(res, 401, false, "Token not active");
    } else {
      formatResponse(res, 401, false, error?.message || "Authentication failed");
    }
    return;
  }
};


export default {
  authenticate,
};
