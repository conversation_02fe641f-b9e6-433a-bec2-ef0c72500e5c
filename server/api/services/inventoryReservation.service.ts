import mongoose from "mongoose";
import Item from "../models/item.model";
import { TransactionManager } from "./transactionManager.service";
import Logger from "../../utils/logUtils";

/**
 * Inventory Reservation System
 * Handles stock reservations during order building process
 */
export class InventoryReservationService {
  private static reservations = new Map<string, {
    items: Array<{itemId: string, quantity: number}>,
    expiresAt: Date,
    orderId?: string,
    customerId?: string,
    restaurantId: string
  }>();

  /**
   * Reserve stock for a pending order (e.g., during cart building)
   */
  static async reserveStock(
    items: Array<{itemId: string, quantity: number}>,
    restaurantId: string,
    customerId: string,
    durationMinutes: number = 15
  ): Promise<{success: boolean, reservationId?: string, errors?: string[]}> {
    
    return TransactionManager.executeWithOptionalTransaction(async (session) => {
      const errors: string[] = [];
      const reservationId = `${customerId}_${Date.now()}`;

      // Check availability including existing reservations
      for (const item of items) {
        const query = Item.findOne({
          _id: item.itemId,
          restaurant: restaurantId
        });

        const currentItem = session ? await query.session(session) : await query;

        if (!currentItem) {
          errors.push(`Item ${item.itemId} not found`);
          continue;
        }

        const reservedQuantity = this.getReservedQuantity(item.itemId);
        const availableStock = currentItem.remainingStock - reservedQuantity;

        if (availableStock < item.quantity) {
          errors.push(
            `Insufficient stock for ${currentItem.name}. ` +
            `Available: ${availableStock}, Requested: ${item.quantity}`
          );
        }
      }
      
      if (errors.length > 0) {
        return { success: false, errors };
      }
      
      // Create reservation
      this.reservations.set(reservationId, {
        items,
        expiresAt: new Date(Date.now() + durationMinutes * 60 * 1000),
        customerId,
        restaurantId
      });
      
      // Schedule cleanup
      setTimeout(() => {
        this.releaseReservation(reservationId);
      }, durationMinutes * 60 * 1000);
      
      Logger.info('Stock reserved successfully', {
        reservationId,
        customerId,
        restaurantId,
        items: items.length,
        expiresIn: durationMinutes
      });
      
      return { success: true, reservationId };
    });
  }

  /**
   * Convert reservation to actual order
   */
  static async confirmReservation(
    reservationId: string,
    orderId: string
  ): Promise<boolean> {
    const reservation = this.reservations.get(reservationId);
    if (!reservation || reservation.expiresAt < new Date()) {
      Logger.warn('Reservation not found or expired', { reservationId, orderId });
      return false;
    }
    
    // Update reservation with order ID
    reservation.orderId = orderId;
    
    Logger.info('Reservation confirmed for order', {
      reservationId,
      orderId,
      items: reservation.items.length
    });
    
    return true;
  }

  /**
   * Release reservation (manual or automatic)
   */
  static releaseReservation(reservationId: string): void {
    const reservation = this.reservations.get(reservationId);
    if (reservation) {
      this.reservations.delete(reservationId);
      Logger.info('Reservation released', {
        reservationId,
        customerId: reservation.customerId,
        items: reservation.items.length
      });
    }
  }

  /**
   * Get total reserved quantity for an item
   */
  private static getReservedQuantity(itemId: string): number {
    let total = 0;
    const now = new Date();
    
    for (const [id, reservation] of this.reservations) {
      if (reservation.expiresAt < now) {
        this.reservations.delete(id);
        continue;
      }
      
      const item = reservation.items.find(i => i.itemId === itemId);
      if (item) {
        total += item.quantity;
      }
    }
    
    return total;
  }

  /**
   * Get all reservations for a customer
   */
  static getCustomerReservations(customerId: string): Array<{
    reservationId: string,
    items: Array<{itemId: string, quantity: number}>,
    expiresAt: Date,
    orderId?: string
  }> {
    const customerReservations: Array<any> = [];
    
    for (const [id, reservation] of this.reservations) {
      if (reservation.customerId === customerId && reservation.expiresAt > new Date()) {
        customerReservations.push({
          reservationId: id,
          items: reservation.items,
          expiresAt: reservation.expiresAt,
          orderId: reservation.orderId
        });
      }
    }
    
    return customerReservations;
  }

  /**
   * Get stock availability including reservations
   */
  static async getAvailableStock(
    itemId: string,
    restaurantId: string
  ): Promise<{
    totalStock: number,
    reservedStock: number,
    availableStock: number
  }> {
    const item = await Item.findOne({
      _id: itemId,
      restaurant: restaurantId
    });
    
    if (!item) {
      return {
        totalStock: 0,
        reservedStock: 0,
        availableStock: 0
      };
    }
    
    const reservedStock = this.getReservedQuantity(itemId);
    const availableStock = Math.max(0, item.remainingStock - reservedStock);
    
    return {
      totalStock: item.remainingStock,
      reservedStock,
      availableStock
    };
  }

  /**
   * Extend reservation duration
   */
  static extendReservation(
    reservationId: string,
    additionalMinutes: number
  ): boolean {
    const reservation = this.reservations.get(reservationId);
    if (!reservation || reservation.expiresAt < new Date()) {
      return false;
    }
    
    reservation.expiresAt = new Date(
      reservation.expiresAt.getTime() + additionalMinutes * 60 * 1000
    );
    
    Logger.info('Reservation extended', {
      reservationId,
      additionalMinutes,
      newExpiresAt: reservation.expiresAt
    });
    
    return true;
  }

  /**
   * Clean up expired reservations
   */
  static cleanupExpiredReservations(): number {
    const now = new Date();
    let cleanedCount = 0;
    
    for (const [id, reservation] of this.reservations) {
      if (reservation.expiresAt < now) {
        this.reservations.delete(id);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      Logger.info(`Cleaned up ${cleanedCount} expired reservations`);
    }
    
    return cleanedCount;
  }

  /**
   * Get reservation statistics
   */
  static getReservationStats(): {
    totalReservations: number,
    activeReservations: number,
    expiredReservations: number
  } {
    const now = new Date();
    let activeCount = 0;
    let expiredCount = 0;
    
    for (const [id, reservation] of this.reservations) {
      if (reservation.expiresAt > now) {
        activeCount++;
      } else {
        expiredCount++;
      }
    }
    
    return {
      totalReservations: this.reservations.size,
      activeReservations: activeCount,
      expiredReservations: expiredCount
    };
  }
}

// Schedule periodic cleanup of expired reservations
setInterval(() => {
  InventoryReservationService.cleanupExpiredReservations();
}, 5 * 60 * 1000); // Every 5 minutes

export default InventoryReservationService;
