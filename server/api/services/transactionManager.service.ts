import mongoose from "mongoose";
import Logger from "../../utils/logUtils";

export class TransactionManager {
  /**
   * Execute a function within a MongoDB transaction
   */
  static async executeInTransaction<T>(
    operation: (session: mongoose.ClientSession) => Promise<T>,
    options?: {
      retries?: number;
      retryDelay?: number;
    }
  ): Promise<T> {
    const { retries = 3, retryDelay = 100 } = options || {};
    
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= retries; attempt++) {
      const session = await mongoose.startSession();
      
      try {
        let result: T;
        
        await session.withTransaction(async () => {
          result = await operation(session);
        });
        
        return result!;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown transaction error');
        
        Logger.warn(`Transaction attempt ${attempt} failed`, {
          error: lastError.message,
          attempt,
          maxRetries: retries
        });
        
        // If this is not the last attempt, wait before retrying
        if (attempt < retries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }
      } finally {
        await session.endSession();
      }
    }
    
    // If we get here, all retries failed
    Logger.error('Transaction failed after all retries', {
      error: lastError?.message,
      retries
    });
    
    throw lastError || new Error('Transaction failed after all retries');
  }

  /**
   * Execute multiple operations in a single transaction
   */
  static async executeMultipleInTransaction(
    operations: Array<(session: mongoose.ClientSession) => Promise<any>>,
    options?: {
      retries?: number;
      retryDelay?: number;
    }
  ): Promise<any[]> {
    return this.executeInTransaction(async (session) => {
      const results = [];
      
      for (const operation of operations) {
        const result = await operation(session);
        results.push(result);
      }
      
      return results;
    }, options);
  }

  /**
   * Check if the current MongoDB connection supports transactions
   */
  static async supportsTransactions(): Promise<boolean> {
    try {
      const admin = mongoose?.connection?.db?.admin();
      const result = await admin?.serverStatus();
      
      // Check if we're running on a replica set or sharded cluster
      return !!(result?.repl || result?.sharding);
    } catch (error) {
      Logger.warn('Could not determine transaction support', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Execute operation with or without transaction based on support
   */
  static async executeWithOptionalTransaction<T>(
    operation: (session?: mongoose.ClientSession) => Promise<T>,
    options?: {
      retries?: number;
      retryDelay?: number;
    }
  ): Promise<T> {
    const supportsTransactions = await this.supportsTransactions();
    
    if (supportsTransactions) {
      return this.executeInTransaction(operation, options);
    } else {
      // Execute without transaction
      Logger.info('Executing operation without transaction (not supported)');
      return operation();
    }
  }
}
