import {  Router } from "express";
import authController from "../controllers/auth.controller";
import authMiddleware from "../middlewares/auth.middleware";
import { upload } from "../utilities/multer";
import constants from "../constants";
import rateLimitingMiddleware from "../middlewares/rateLimiting.middleware";

const router = Router();
router.post(
  "/auth/register",
  upload.fields([
    { name: "profileImg", maxCount: 1 },
    { name: "docs", maxCount: constants.MAXUSERDOCSIZE },
  ]),
  authController.register
);

// Conditionally apply rate limiting - skip in test environment
const authMiddlewares = process.env.NODE_ENV === 'test'
  ? [authController.login]
  : [rateLimitingMiddleware.authRateLimit, authController.login];

router.post("/auth/login", ...authMiddlewares);
router.post("/auth/logout", authController.logout);
router.post("/auth/refresh", authController.refresh);
export default router;
