
import authMiddleware from "../middlewares/auth.middleware";
import RestaurantController from "../controllers/restaurant.controller";
import createCRUDRoutes from "../utilities/createControllerRoute";
import { upload } from "../utilities/multer";
import rateLimitingMiddleware from "../middlewares/rateLimiting.middleware";

const routeName = "/restaurant";
const router = createCRUDRoutes(routeName, RestaurantController, {
  getAll: [rateLimitingMiddleware.apiRateLimit, authMiddleware.authenticate],
  create: [rateLimitingMiddleware.apiRateLimit, authMiddleware.authenticate, upload.single("logo")],
  update: [rateLimitingMiddleware.apiRateLimit, authMiddleware.authenticate],
  delete: [rateLimitingMiddleware.apiRateLimit, authMiddleware.authenticate],
  getMyProfile: [rateLimitingMiddleware.apiRateLimit, authMiddleware.authenticate],
  getById: [rateLimitingMiddleware.apiRateLimit, authMiddleware.authenticate],
});
export default router;
