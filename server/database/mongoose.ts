import mongoose from "mongoose";
import Logger from "../utils/logUtils";
import constants from "../api/constants";

export const connectDB = async () => {
  try {
    // In test environment, use MONGO_TEST if available, otherwise use constants.URI
    const dbUri = process.env.NODE_ENV === 'test'
      ? (process.env.MONGO_TEST || constants.URI)
      : constants.URI;

    if (!dbUri) {
      Logger.warn("No database URI provided, skipping connection");
      return;
    }

    await mongoose.connect(dbUri, {});
    Logger.info("Database connected successfully");
  } catch (error) {
    Logger.error(`Error in Connecting to database`, error);
  }
};

